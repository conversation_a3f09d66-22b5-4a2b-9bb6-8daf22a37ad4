import asyncio

from src.api import *
from src.core import mcp_server
from src.infra.app import App

if __name__ == "__main__":
    try:
        app = App(config=http_server.config)

        asyncio.run(app.async_init())

        app.add_on_start(lambda: asyncio.run(mcp_server.launch(http_server.config.mcp_app)))
        app.launch()
    except KeyboardInterrupt:
        print("\nReceived shutdown signal")
