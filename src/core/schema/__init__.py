from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class CreateMCPServerReq(BaseModel):
    server_name: str = Field(..., description="服务器名称")
    server_url: str = Field(..., description="服务器地址")
    public_status: int = Field(default=1, description="公开范围 1-公开 0-私有")
    emails: Optional[List[str]] = Field([], description="私有人邮箱")
    created_by: str = Field(..., description="创建人邮箱")


class CreateMCPServerToolDomainReq(BaseModel):
    domain_id: str = Field(..., description="领域ID")
    domain_name: str = Field(..., description="领域名称")
    description: str = Field(..., description="领域描述")
    created_by: str = Field(..., description="创建人")


class CreateMCPDomainDescriptionReq(BaseModel):
    domain_id: str = Field(..., description="领域ID")


class CreateMCPToolDescriptionReq(BaseModel):
    server_id: int = Field(..., description="服务器ID")
    tool_name: str = Field(..., description="工具名称")


class UpdateMCPServerToolDomainReq(BaseModel):
    domain_id: str = Field(..., description="领域ID")
    domain_name: str = Field(..., description="领域名称")
    description: str = Field(..., description="领域描述")
    updated_by: str = Field(..., description="更新人")


class ListMCPServerToolReq(BaseModel):
    server_id: Optional[int] = Field(None, description="服务器ID")
    domain: Optional[str] = Field(None, description="域名")
    tool_name: Optional[str] = Field(None, description="工具名称")


class UpdateMCPServerReq(BaseModel):
    server_id: int = Field(..., description="服务器ID")
    server_name: str = Field(..., description="服务器名称")
    public_status: int = Field(default=..., description="公开范围 1-公开 0-私有")
    server_online: int = Field(..., description="服务器在线状态")
    emails: Optional[List[str]] = Field([], description="私有人邮箱")
    updated_by: str = Field(..., description="更新人邮箱")


class UpdateMCPServerOnlineStatusReq(BaseModel):
    server_id: int = Field(..., description="服务器ID")
    server_online: int = Field(..., description="服务器在线状态")


class UpdateMCPServerToolReq(BaseModel):
    server_id: int = Field(..., description="服务器ID")
    tool_name: str = Field(..., description="工具名称")
    need_require_auth: int = Field(..., description="是否需要二次授权")
    tool_domains: Optional[List[str]] = Field([], description="工具领域")
    description: str = Field(..., description="工具描述")


class MCPServerToolResult(BaseModel):
    server_id: int = Field(..., description="服务器ID")
    server_name: str = Field(..., description="服务器名称")
    server_url: str = Field(..., description="服务器地址")
    tool_name: str = Field(..., description="工具名称")
    tool_domains: Optional[List[str]] = Field([], description="工具领域")
    description: str = Field(..., description="工具描述")
    need_require_auth: Optional[int] = Field(default=0, description="是否需要二次授权")
    updated_at: Optional[datetime] = Field(default="", description="更新时间")
