import typing
from sqlalchemy import <PERSON>umn, Integer, String, ForeignKey, ARRAY, Text
from sqlalchemy.orm import relationship

from src.infra.clients.orm import IdModelMixin, DateTimeModelMixin


class MCPServer(IdModelMixin, DateTimeModelMixin):
    __tablename__ = "mcp_server"
    __allow_unmapped__ = True
    server_name = Column(String(255), unique=True, nullable=False)

    server_url = Column(String(500), unique=True, nullable=False)
    emails = Column(ARRAY(Text), nullable=True)
    server_online = Column(Integer, default=1, nullable=False)
    public_status = Column(Integer, default=1, nullable=False)
    tool_count = Column(Integer, default=0, nullable=False)
    server_status = Column(String(255), default="running success", nullable=False)
    created_by = Column(String(255), nullable=True)
    updated_by = Column(String(255), nullable=True)

    # Relationship: one server has many tools
    tools: typing.List["MCPServerTool"] = relationship(
        'MCPServerTool',
        back_populates='server',
        primaryjoin='MCPServer.id==foreign(MCPServerTool.server_id)',
        uselist=True,
        lazy="joined"
    )


class MCPServerTool(IdModelMixin, DateTimeModelMixin):
    __tablename__ = "mcp_server_tool"
    __allow_unmapped__ = True

    tool_name = Column(String(255), unique=True, nullable=False)
    tool_domains = Column(ARRAY(Text), nullable=True)
    need_require_auth = Column(Integer, default=0, nullable=False)
    server_id = Column(Integer, ForeignKey("mcp_server.id"), nullable=False)
    description = Column(Text, nullable=True)

    server: "MCPServer" = relationship(
        'MCPServer',
        back_populates='tools',
        primaryjoin='MCPServer.id==foreign(MCPServerTool.server_id)',
        uselist=False,
        lazy="joined"
    )


class MCPServerDomain(IdModelMixin, DateTimeModelMixin):
    __tablename__ = "mcp_server_domain"
    __allow_unmapped__ = True
    domain_id = Column(String(255), unique=True, nullable=False)
    domain_name = Column(String(255), unique=True, nullable=False)
    description = Column(Text, nullable=False)
    created_by = Column(String(255), nullable=True)
    updated_by = Column(String(255), nullable=True)
