import asyncio
from typing import List, Dict

from mcp import <PERSON><PERSON><PERSON><PERSON><PERSON>, Tool, types
from mcp.client.sse import sse_client

from src.core.repo.postgre.mcp import MCPServerTool, MCPServer
from src.infra.clients.orm import orm_session
from src.infra.web.log import logger


class MCPSession:
    def __init__(self, name: str, url: str):
        self.url = url
        self.name = name

    async def initialize(self):
        async with sse_client(url=self.url) as (read, write):
            session = ClientSession(read, write)
            async with session:
                await session.initialize()

    async def list_tools(self) -> types.ListToolsResult:
        try:
            async with sse_client(url=self.url) as (read, write):
                session = ClientSession(read, write)
                async with session:
                    await session.initialize()
                    tools = await session.list_tools()
                    return tools
        except Exception as e:
            logger.error(f"Error getting tools from MCP session {self.url}: {e}")
            return types.ListToolsResult(tools=[])


class SessionTool:
    def __init__(self, domains: List[str], session: MCPSession, tool: Tool, need_require_auth: int):
        self.domains = domains
        self.session = session
        self.tool = tool
        self.need_require_auth = need_require_auth


class ServerTool:
    url: str
    tools: List[MCPServerTool]

    def __init__(self, url: str, tools: List[MCPServerTool]):
        self.url = url
        self.tools = tools


class MultiSessionManager:
    def __init__(self):
        self.sessions: Dict[str, MCPSession] = {}

    def add_mcp_server(self, name: str, url: str):
        if url not in self.sessions:
            self.sessions[url] = MCPSession(name, url)

    async def add_mcp_session(self, name: str, url: str):
        session = MCPSession(name, url)
        await session.initialize()
        if session.url not in self.sessions:
            self.sessions[session.url] = session

    def remove_mcp_session(self, url: str):
        """Simply remove the session from the dictionary"""
        if url in self.sessions:
            del self.sessions[url]

    async def get_session_tools(self, domain: str, email: str) -> List[SessionTool]:
        # Helper function to process a single session
        async def process_session(mcp_session, server_tools_list, domain_filter):
            try:
                session_tool_list = []

                # Find the matching ServerTool for this session
                matching_server_tool = None
                for server_tool in server_tools_list:
                    if server_tool.url == mcp_session.url:
                        matching_server_tool = server_tool
                        break

                # If no matching server tool found, return empty list
                if not matching_server_tool:
                    return []

                cmp_tools = await mcp_session.list_tools()
                for cmp_tool in cmp_tools.tools:
                    tool_domains = []
                    need_require_auth = 0

                    for tool in matching_server_tool.tools:
                        if cmp_tool.name == tool.tool_name:
                            cmp_tool.description = tool.description if tool.description and tool.description != "" \
                                else cmp_tool.description
                            need_require_auth = tool.need_require_auth
                            tool_domains.extend(tool.tool_domains)

                    # Skip tools that don't have domains or if domain_filter is not in tool_domains
                    if not tool_domains or domain_filter not in tool_domains:
                        continue

                    session_tool_list.append(SessionTool(tool_domains, mcp_session, cmp_tool, need_require_auth))
                return session_tool_list
            except Exception as e:
                logger.error(f"Error getting tools from MCP session {mcp_session.url}: {e}")
                self.remove_mcp_session(mcp_session.url)
                return []

        all_tools = []
        server_tools = []

        with orm_session() as sess:
            try:
                stmt = sess.query(MCPServer).where(MCPServer.server_online == 1)
                result = sess.execute(stmt)
                servers = result.scalars().all()
                if not servers:
                    return []
                for server in servers:
                    if server.public_status == 0:
                        if email not in server.emails:
                            continue
                    stmt = sess.query(MCPServerTool).where(MCPServerTool.server_id == server.id
                                                           and MCPServerTool.tool_domains != None)
                    result = sess.execute(stmt)
                    tools = result.scalars().all()
                    if not tools:
                        continue
                    server_tools.append(
                        ServerTool(
                            url=server.server_url,
                            tools=tools
                        )
                    )

            except Exception as e:
                logger.error(f"Error getting tools from MCP session: {e}")

        tasks = [process_session(session, server_tools, domain) for session in self.sessions.values()]
        results = await asyncio.gather(*tasks)
        for session_tools in results:
            all_tools.extend(session_tools)

        return all_tools
