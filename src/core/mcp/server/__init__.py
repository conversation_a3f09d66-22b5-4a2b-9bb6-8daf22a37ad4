from typing import Any

import uvicorn
from mcp import types, ClientSession
from mcp.client.sse import sse_client
from starlette.applications import Starlette
from starlette.routing import Route, Mount

from src.infra.clients.orm import orm_session
from src.infra.config.config import <PERSON><PERSON><PERSON>
from src.infra.web.log import logger
from .server import *
from .server import DomainAwareServer

__all__ = [
    "mcp_server",
]

from ...repo.postgre.mcp import MCPServerDomain


class CustomTool(types.Tool):
    def __init__(self, name: str, description: str, inputSchema: dict, need_require_auth: bool = False):
        super().__init__(name=name, description=description, inputSchema=inputSchema)
        self.need_require_auth = need_require_auth


class OPSBrainMCPServer:

    def __init__(self):
        self.domain_server = DomainAwareServer("ops-brain-mcp-server")

        @self.domain_server.list_tools()
        async def list_tools(req: dict[str, Any]) -> list[types.Tool]:
            domain = req['domain']
            email = req['email']
            logger.info(f"Listing tools for domain: {domain}, email: {email}")

            tools = []
            session_tools = await self.domain_server.multi_manager.get_session_tools(domain, email)
            for sessionTool in session_tools:
                tool = sessionTool.tool
                if 'properties' in tool.inputSchema:
                    if 'email' in tool.inputSchema['properties']:
                        del tool.inputSchema['properties']['email']
                        if 'required' in tool.inputSchema and 'email' in tool.inputSchema['required']:
                            tool.inputSchema['required'].remove('email')

                tools.append(CustomTool(
                    name=tool.name,
                    description=tool.description,
                    inputSchema=tool.inputSchema,
                    need_require_auth=sessionTool.need_require_auth == 1
                ))
            return tools

        @self.domain_server.call_tool()
        async def handle_call_tool(name: str, arguments: dict, model_extra: dict) -> list[types.TextContent]:

            logger.info(f"handle_call_tool called name={name} and arguments={arguments}")

            if "domain" not in model_extra or not model_extra["domain"]:
                return [types.TextContent(text="Domain not found", type="text")]

            domain = model_extra["domain"]
            session_tools = await self.domain_server.multi_manager.get_session_tools(domain, "")
            for sessionTool in session_tools:
                if sessionTool.tool.name == name:
                    async with sse_client(url=sessionTool.session.url) as (read, write):
                        session = ClientSession(read, write)
                        async with session:
                            await session.initialize()
                            try:
                                result = await session.call_tool(name, arguments)
                                logger.info(f"handle_call_tool result={result.content}")
                                return result.content
                            except Exception as e:
                                logger.error(f"handle_call_tool error={e}")
                                return [types.TextContent(text="handle_call_tool error " + str(e), type="text")]
            return [types.TextContent(text="Tool not found", type="text")]

        @self.domain_server.list_prompts()
        async def list_prompts() -> list[types.Prompt]:
            prompts = []
            with orm_session() as sess:
                domains = sess.query(MCPServerDomain).all()
                for domain in domains:
                    prompts.append(types.Prompt(
                        name=domain.domain_id,
                        description=domain.description
                    ))
            return prompts

    async def launch(self, mcp_app: MCPApp):
        await self.domain_server.connect_servers()
        starlette_app = Starlette(
            routes=[
                Route("/sse/{domain}/{email}/", endpoint=self.domain_server.handle_sse),
                Mount("/messages/{domain}/{email}/", app=self.domain_server.handle_post_message)
            ]
        )
        config = uvicorn.Config(starlette_app, host=mcp_app.host, port=mcp_app.port, log_level="info")
        launch_server = uvicorn.Server(config)
        await launch_server.serve()


mcp_server = OPSBrainMCPServer()
