from contextlib import asynccontextmanager
from typing import Any, Callable, Awaitable, Iterable
from urllib.parse import quote
from uuid import UUID
from uuid import uuid4

import anyio
import mcp.types as types
from anyio.streams.memory import MemoryObjectReceiveStream, MemoryObjectSendStream
from mcp.server import Server
from mcp.server.sse import SseServerTransport
from pydantic import ValidationError
from sqlalchemy import select
from sse_starlette import EventSourceResponse
from starlette.requests import Request
from starlette.responses import Response
from starlette.types import Scope, Receive, Send

from src.core.mcp.session.session import MultiSessionManager, MCPSession

__all__ = [
    "DomainAwareServer"
]

from src.core.repo.postgre.mcp import MCPServer

from src.infra.clients.orm import orm_session

from src.infra.web.log import logger


class DomainAwareServer(Server):
    multi_manager: MultiSessionManager
    sse: SseServerTransport

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.sse = SseServerTransport("/messages/")

    async def connect_servers(self):
        self.multi_manager = MultiSessionManager()

        with orm_session() as sess:
            stmt = select(MCPServer)
            result = sess.execute(stmt)
            servers = result.unique().scalars().all()
        for server in servers:
            try:
                if server.server_status != "offline":
                    session = MCPSession(server.server_name, server.server_url)
                    await session.initialize()
                    if session.url not in self.multi_manager.sessions:
                        self.multi_manager.sessions[session.url] = session
                    logger.info(f"Successfully initialized server: {server.server_name}")
                    self.update_server_status(server, 1, "running success")
            except Exception as e:
                logger.error(f"Failed to initialize server {server.server_name}: {str(e)}")

    @staticmethod
    def update_server_status(server, server_online: int, status: str):
        try:
            with orm_session() as sess:
                db_server = sess.query(MCPServer).filter(MCPServer.id == server.id).first()
                if db_server:
                    db_server.server_online = server_online
                    db_server.server_status = status
                    sess.commit()
                else:
                    logger.warning(f"Could not find server with server_name {server.server_name} to update status")
        except Exception as db_error:
            logger.error(f"Database error while updating server status: {str(db_error)}")

    def list_tools(self):
        def decorator(func):
            logger.debug(f"Registering handler for ListToolsRequest with function that accepts parameters")

            async def handler(req: Any):
                params = req.model_extra
                if params is None:
                    params = {}

                tools = await func(params)
                return types.ServerResult(types.ListToolsResult(tools=tools))

            self.request_handlers[types.ListToolsRequest] = handler
            return func

        return decorator

    def call_tool(self):
        def decorator(
                func: Callable[
                    ...,
                    Awaitable[
                        Iterable[
                            types.TextContent | types.ImageContent | types.EmbeddedResource
                            ]
                    ],
                ],
        ):
            logger.debug("Registering handler for CallToolRequest")

            async def handler(req: types.CallToolRequest):
                try:
                    model_extra = req.model_extra
                    if model_extra is None:
                        model_extra = {}
                    results = await func(req.params.name, (req.params.arguments or {}), model_extra)
                    return types.ServerResult(
                        types.CallToolResult(content=list(results), isError=False)
                    )
                except Exception as e:
                    return types.ServerResult(
                        types.CallToolResult(
                            content=[types.TextContent(type="text", text=str(e))],
                            isError=True,
                        )
                    )

            self.request_handlers[types.CallToolRequest] = handler
            return func

        return decorator

    async def handle_sse(self, request):
        domain = request.path_params['domain']
        email = request.path_params['email']
        logger.info(f"Connection request from domain: {domain}")
        async with self.connect_sse(request.scope, request.receive, request._send, domain, email) as streams:
            await self.run(streams[0], streams[1], self.create_initialization_options())

    @asynccontextmanager
    async def connect_sse(self, scope: Scope, receive: Receive, send: Send, domain: str, email: str):

        if scope["type"] != "http":
            logger.error("connect_sse received non-HTTP request")
            raise ValueError("connect_sse can only handle HTTP requests")

        logger.debug("Setting up SSE connection")
        read_stream: MemoryObjectReceiveStream[types.JSONRPCMessage | Exception]
        read_stream_writer: MemoryObjectSendStream[types.JSONRPCMessage | Exception]

        write_stream: MemoryObjectSendStream[types.JSONRPCMessage]
        write_stream_reader: MemoryObjectReceiveStream[types.JSONRPCMessage]

        read_stream_writer, read_stream = anyio.create_memory_object_stream(0)
        write_stream, write_stream_reader = anyio.create_memory_object_stream(0)

        session_id = uuid4()
        session_uri = f"{quote(self.sse._endpoint)}{domain}/{email}/?session_id={session_id.hex}"
        self.sse._read_stream_writers[session_id] = read_stream_writer
        logger.debug(f"Created new session with ID: {session_id}")

        sse_stream_writer, sse_stream_reader = anyio.create_memory_object_stream[
            dict[str, Any]
        ](0)

        async def sse_writer():
            logger.debug("Starting SSE writer")
            async with sse_stream_writer, write_stream_reader:
                await sse_stream_writer.send({"event": "endpoint", "data": session_uri})
                logger.debug(f"Sent endpoint event: {session_uri}")

                async for message in write_stream_reader:
                    logger.debug(f"Sending message via SSE: {message}")
                    await sse_stream_writer.send(
                        {
                            "event": "message",
                            "data": message.model_dump_json(
                                by_alias=True, exclude_none=True
                            ),
                        }
                    )

        async with anyio.create_task_group() as tg:
            response = EventSourceResponse(
                content=sse_stream_reader, data_sender_callable=sse_writer
            )
            logger.debug("Starting SSE response task")
            tg.start_soon(response, scope, receive, send)

            logger.debug("Yielding read and write streams")
            yield read_stream, write_stream

    async def handle_post_message(self, scope: Scope, receive: Receive, send: Send
                                  ) -> None:
        logger.debug("Handling POST message")
        request = Request(scope, receive)

        session_id_param = request.query_params.get("session_id")
        if session_id_param is None:
            logger.warning("Received request without session_id")
            response = Response("session_id is required", status_code=400)
            return await response(scope, receive, send)

        try:
            session_id = UUID(hex=session_id_param)
            logger.debug(f"Parsed session ID: {session_id}")
        except ValueError:
            logger.warning(f"Received invalid session ID: {session_id_param}")
            response = Response("Invalid session ID", status_code=400)
            return await response(scope, receive, send)

        writer = self.sse._read_stream_writers.get(session_id)
        if not writer:
            logger.warning(f"Could not find session for ID: {session_id}")
            response = Response("Could not find session", status_code=404)
            return await response(scope, receive, send)

        json = await request.json()
        if json.get('method') == 'tools/list' or json.get('method') == 'tools/call':
            if 'domain' not in json:
                json['domain'] = ''
            json['domain'] = request.path_params['domain']

        if json.get('method') == 'tools/list':
            if 'email' not in json:
                json['email'] = ''
            email = request.path_params['email']
            if email:
                json['email'] = request.path_params['email']
        logger.info(f"Received JSON: {json}")

        try:
            message = types.JSONRPCMessage.model_validate(json)
            logger.info(f"Validated client message: {message}")
        except ValidationError as err:
            logger.error(f"Failed to parse message: {err}")
            response = Response("Could not parse message", status_code=400)
            await response(scope, receive, send)
            await writer.send(err)
            return

        logger.debug(f"Sending message to writer: {message}")
        response = Response("Accepted", status_code=202)
        await response(scope, receive, send)
        await writer.send(message)
