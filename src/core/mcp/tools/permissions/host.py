from typing import List

from mcp.server import FastMCP
from pydantic import Field

from src.infra.apis.host import HostAPIWrapper
from src.infra.config import MCPConfig
from src.infra.web.log import logger


def register_host_tools(mcp_instance: FastMCP, config: MCPConfig):
    @mcp_instance.tool(name="apply_host_auth",
                       description='''
                       - 功能说明：
                           工具用于发起对堡垒机系统中指定主机账号的访问权限申请流程,该工具专注于权限申请操作，不具备查询主机信息、远程登录或执行命令等其他功能。
                       - 输入参数：
                           hosts：主机名称或IP地址（必填），支持多个主机，使用英文逗号分隔。
                           accounts：主机账号列表(必填)，支持多个账号，使用英文逗号分隔。
                       - 注意事项：
                           1.禁止申请root账号。
                           2.当用户未提供账号时，必须说明：可申请账号：__su账号、godman账号、log_view账号、同名账号(用户邮箱前缀)。
                       - 核心特性：
                           支持批量主机与账号权限申请
                       - 典型场景：
                           用户需要访问一个主机或一组主机以进行运维或开发操作，通过该工具申请对应主机账号的访问权限。
                       ''')
    async def apply_host_auth(
            hosts: List[str] = Field(..., description="主机名称或者IP地址列表"),
            accounts: List[str] = Field(..., description="主机账号列表"),
            email: str = Field(..., description="申请人邮箱")
    ):
        data = {
            "hosts": hosts,
            "accounts": accounts,
            "email": email,
        }
        try:
            response = HostAPIWrapper(config).post(
                "/tt-cloud-auth-platform-web/rest/v1/host/auth", json=data)
            if response.status_code == 200:
                return response.json()["data"]
            else:
                return "服务异常，请稍后重试。"
        except Exception as e:
            logger.error(f"apply_host_auth error: {e}")
            return f"服务异常，请稍后重试。{str(e)}"
