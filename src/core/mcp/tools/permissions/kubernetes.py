import json
from typing import Literal

from mcp.server import FastMCP
from pydantic import Field

from src.infra.apis.host import HostAPIWrapper
from src.infra.config import MCPConfig
from src.infra.web.log import logger


def register_kubernetes_tools(mcp_instance: FastMCP, config: MCPConfig):
    @mcp_instance.tool(name="ApplyKubeConfigRole",
                       description=''' 
                       - 功能说明：
                           ApplyKubeConfigRole工具用于用户在Kubernetes集群中申请特定角色的kubeConfig权限，以实现对集群的操作。使用该工具，用户需明确申请的权限类型。
                       - 输入参数：
                           cluster_name: str - Kubernetes集群名称，用户必须指定。
                           namespace: str - Kubernetes命名空间，用户必须提供。
                           role_name: Literal['admin', 'develop', 'view', 'edit'] - 申请的角色名称，必须为指定四种角色之一。
                       - 注意事项(必须要告知用户的内容):
                           1.此工具仅用于申请kubeConfig权限，无法执行查询或操作Kubernetes的功能。
                           2.在生产环境中，建议申请角色为view或develop，以限制操作权限。
                           3.工单暂时不支持集群级别的kubeConfig权限申请，如有需要，请联系业务运维进行手动开通。
                       - 核心特性：
                           便捷权限申请：用户通过指定必要参数快速申请kubeConfig权限。
                           安全性：角色限制确保权限申请在规定范围内。
                       - 典型场景：
                           用户需要操作特定Kubernetes集群并申请所需角色权限，以便进行开发、查看或编辑。
                           在生产环境中，用户使用该工具申请view或develop角色以获得基本操作权限。
                       ''')
    async def ApplyKubeConfigRole(
            cluster_name: str = Field(..., description="kubernetes集群名称"),
            namespace: str = Field(..., description="kubernetes命名空间"),
            role_name: Literal['admin', 'develop', 'view', 'edit'] = Field(..., description="角色名称"),
            email: str = Field(..., description="权限申请人邮箱", )
    ):
        headers = {"Content-Type": "application/json"}
        payload = {
            "cluster_name": cluster_name,
            "namespace": namespace,
            "role_name": role_name,
            "email": email,
            "apply_type": "kubeConfig",
        }
        data = json.dumps(payload)
        logger.debug(f"payload: {data}")
        response = HostAPIWrapper(config).post("/tt-cloud-auth-platform-web/rest/v1/kubernetes/auth", data=data,
                                               headers=headers)
        logger.debug(f"response: {response}")
        if response.status_code == 200:
            return response.json()["data"]
        else:
            return "服务异常，请稍后重试。"

    @mcp_instance.tool(name="ApplyRoleInCluster",
                       description='''
                       - 功能说明：
                           ApplyRoleInCluster工具是用于申请牵星平台内Kubernetes集群角色权限的工具。用户通过此工具可以向牵星平台发起特定角色权限的申请。
                       - 输入参数：
                           cluster_name: str - Kubernetes集群名称，必须指定。
                           namespace: str - Kubernetes命名空间，必须提供。
                           role_name: Literal['view', 'edit'] - 申请的角色名称，限定为view或edit。
                       - 注意事项(必须要告知用户的内容):
                           工具仅用于申请牵星平台Kubernetes集群的角色权限，不能用于直接操作Kubernetes或牵星平台。
                           申请完成后，用户将在牵星-容器云平台获得相应的Kubernetes资源操作权限。
                       - 核心特性：
                           简化申请流程：通过必要信息直接发送权限申请请求。
                           适用性广泛：通过牵星平台满足用户对Kubernetes资源的常规操作需求。
                       - 典型场景：
                           用户需要在牵星平台内进行Kubernetes资源管理操作并申请相应的操作权限。
                           使用者通过提供必须参数快速申请角色权限以执行特定的开发或编辑任务。
                       ''')
    async def ApplyRoleInCluster(
            cluster_name: str = Field(..., description="kubernetes集群名称"),
            namespace: str = Field(..., description="kubernetes命名空间"),
            role_name: Literal['view', 'edit'] = Field(..., description="角色名称"),
            email: str = Field(..., description="权限申请人邮箱", )
    ):
        headers = {"Content-Type": "application/json"}
        payload = {
            "cluster_name": cluster_name,
            "namespace": namespace,
            "role_name": role_name,
            "email": email,
            "apply_type": "kubernetes",
        }
        data = json.dumps(payload)
        logger.debug(f"payload: {data}")
        response = HostAPIWrapper(config).post("/tt-cloud-auth-platform-web/rest/v1/kubernetes/auth", data=data,
                                               headers=headers)
        logger.debug(f"response: {response}")
        if response.status_code == 200:
            return response.json()["data"]
        else:
            return "服务异常，请稍后重试。"
