import json
from urllib import request

import requests
from mcp.server import FastMCP
from pydantic import Field

from src.infra.config import MCPConfig
from src.infra.web.log import logger


def register_error_code_tools(mcp_instance: FastMCP, config: MCPConfig):
    @mcp_instance.tool(name="get_error_msg_by_code",
                       description='''
                       - 功能说明：
                           错误详情查询工具，输入错误码，查询错误详情
                       - 输入参数：
                           error_code：错误码（必填），例如：-109999
                       - 核心特性：
                           根据错误码查询错误信息详情
                       - 典型场景：
                           用户根据错误码需要查询错误信息详情
                       ''')
    async def get_error(
            error_code: int = Field(..., description="错误码，例如：-109999"),
    ):
        try:
            wiki_token = config.error_code.wiki_took
            wiki_sheet = config.error_code.wiki_sheet + '!A:C'
            url1 = f'https://open.feishu.cn/open-apis/wiki/v2/spaces/get_node'
            headers1 = {
                "Authorization": "Bearer " + get_tenant_access_token(config)
            }
            params = {
                "token": wiki_token
            }
            response1 = requests.get(url=url1, headers=headers1, params=params, timeout=5).json()
            obj_token = response1["data"]["node"]["obj_token"]
            url2 = f'https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{obj_token}/values/{wiki_sheet}'
            headers2 = {
                "Authorization": "Bearer " + get_tenant_access_token(config),
                "Content-Type": "application/json"
            }

            sheet_data = requests.get(url=url2, headers=headers2, timeout=5).json()['data']['valueRange']['values']

            for row in sheet_data:
                if row[1] == int(error_code) or row[1] == int(error_code) * -1:
                    return f'''
                    服务名称：{row[0]}
                    错误号码：{row[1]}
                    错误注释：{row[2]}
                    '''
            return "未找到错误码对应的错误信息"
        except Exception as e:
            logger.error(f"get_error_msg_by_code error: {e}")
            return f"服务异常，请稍后重试。{str(e)}"


def get_tenant_access_token(config: MCPConfig):
    app_id = config.jarvis.app_id
    app_secret = config.jarvis.app_secret
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
    headers = {
        "Content-Type": "application/json"
    }
    req_body = {
        "app_id": app_id,
        "app_secret": app_secret
    }

    data = bytes(json.dumps(req_body), encoding='utf8')
    req = request.Request(url=url, data=data, headers=headers, method='POST')
    try:
        response = request.urlopen(req)
    except Exception as e:
        logger.error(f"[GET_TENANT_ACCESS_TOKEN] Error: {e}")
        return False

    rsp_body = response.read().decode('utf-8')
    rsp_dict = json.loads(rsp_body)
    code = rsp_dict.get("code", -1)
    if code != 0:
        logger.error(f"[GET_TENANT_ACCESS_TOKEN] Error: Code ={code}")
        return False
    return rsp_dict.get("tenant_access_token", "")
