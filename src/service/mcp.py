import asyncio
from functools import cache
from typing import Dict, Any

from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_openai import AzureChatOpenAI
from mcp import ClientSession
from mcp.client.sse import sse_client
from sqlalchemy import select, and_

from src.core import mcp_server
from src.core.repo.postgre.mcp import MCPServer, MCPServerTool, MCPServerDomain
from src.core.schema import *
from src.core.schema import MCPServerToolResult
from src.infra.app import app
from src.infra.clients.orm import orm_session
from src.infra.tools.exceptions import ServiceError
from src.infra.web.log import logger


@cache
def get_llm() -> AzureChatOpenAI:
    return AzureChatOpenAI(
        deployment_name=app.config.llm.deployment_name,
        azure_endpoint=app.config.llm.azure_endpoint,
        openai_api_version=app.config.llm.openai_api_version,
        openai_api_key=app.config.llm.openai_api_key,
    )


class MCPService:
    @staticmethod
    async def list_mcp_server() -> List[Dict[str, Any]]:
        with orm_session() as sess:
            stmt = select(MCPServer)
            result = sess.execute(stmt)
            servers = result.unique().scalars().all()
            sess.commit()
            tools = await MCPService.list_mcp_server_tool(server_id=0, domain="", tool_name="")
            for server in servers:
                tool_count = 0
                for tool in tools:
                    if tool.server_id == server.id:
                        tool_count += 1
                if server.tool_count != tool_count:
                    server.tool_count = tool_count
                    sess.commit()
        return [item.to_dict() for item in servers]

    @staticmethod
    async def list_mcp_server_tool(server_id: Optional[int], domain: Optional[str], tool_name: Optional[str]) -> List[
        MCPServerToolResult]:
        async def get_server_tools(_server):
            try:
                async with sse_client(url=_server.server_url) as (read, write):
                    session = ClientSession(read, write)
                    async with session:
                        await session.initialize()
                        tools = await session.list_tools()
                        try:
                            # Create a dictionary to track unique tool names
                            unique_tools_dict = {}

                            # Process each tool, keeping only the first occurrence of each tool name
                            for tool in tools.tools:
                                if tool.name not in unique_tools_dict:
                                    unique_tools_dict[tool.name] = MCPServerToolResult(
                                        server_id=_server.id,
                                        server_name=_server.server_name,
                                        server_url=_server.server_url,
                                        tool_name=tool.name,
                                        description=tool.description,
                                    )

                            # Convert the dictionary values back to a list
                            return list(unique_tools_dict.values())
                        except Exception as e:
                            raise e
            except Exception as e:
                logger.error(f"Error fetching tools from server {_server.id}: {str(e)}")
                return []

        with orm_session() as sess:
            # Query servers
            stmt = sess.query(MCPServer).where(MCPServer.server_online == 1)
            if server_id:
                stmt = stmt.where(MCPServer.id == server_id and MCPServer.server_online == 1)
            result = sess.execute(stmt)
            servers = result.unique().scalars().all()

            # Concurrently fetch tools from all servers
            tasks = [get_server_tools(server) for server in servers]
            server_tools_list = await asyncio.gather(*tasks)

            # Flatten the list of lists
            mcp_tools = [tool for tools in server_tools_list for tool in tools]

            # Query existing tool records
            stmt = sess.query(MCPServerTool)
            filters = []
            if server_id:
                filters.append(MCPServerTool.server_id == server_id)
            if tool_name:
                filters.append(MCPServerTool.tool_name == tool_name)
            if filters:
                stmt = stmt.where(*filters)
            result = sess.execute(stmt)
            tools = result.unique().scalars().all()

            # Match tools with their domains
            for tool in mcp_tools:
                for _tool in tools:
                    if tool.tool_name == _tool.tool_name and _tool.server_id == tool.server_id:
                        tool.tool_domains = _tool.tool_domains
                        tool.updated_at = _tool.updated_at
                        tool.need_require_auth = _tool.need_require_auth
                        tool.description = _tool.description if _tool.description and _tool.description != "" else tool.description
                        break

            # Filter by domain and tool name if specified
            if domain:
                mcp_tools = [tool for tool in mcp_tools if domain in tool.tool_domains]
            if tool_name:
                mcp_tools = [tool for tool in mcp_tools if tool.tool_name == tool_name]

        return mcp_tools

    @staticmethod
    async def get_mcp_server_tool(server_id: Optional[int], tool_name: Optional[str]) -> MCPServerToolResult | None:
        with orm_session() as sess:
            server = sess.query(MCPServer).filter(MCPServer.id == server_id).first()
            async with sse_client(url=server.server_url) as (read, write):
                session = ClientSession(read, write)
                async with session:
                    await session.initialize()
                    tools = await session.list_tools()
                    try:
                        for tool in tools.tools:
                            if tool.name == tool_name:
                                return MCPServerToolResult(
                                    server_id=server.id,
                                    server_name=server.server_name,
                                    server_url=server.server_url,
                                    tool_name=tool.name,
                                    description=tool.description,
                                )
                    except Exception as e:
                        raise e
        return None

    @staticmethod
    def update_mcp_server(req: UpdateMCPServerReq):
        with orm_session() as sess:
            _server = sess.query(MCPServer).filter(MCPServer.id == req.server_id).first()
            if not _server:
                raise ServiceError("MCP Server not found")
            _server.server_name = req.server_name or _server.server_name
            _server.server_online = 1 if req.server_online > 0 else 0
            _server.public_status = 1 if req.public_status > 0 else 0
            _server.emails = [] if req.public_status > 0 else req.emails or _server.emails
            _server.updated_by = req.updated_by
            sess.commit()
        return _server.to_dict()

    @staticmethod
    async def update_mcp_server_online_status(req: UpdateMCPServerOnlineStatusReq):
        with orm_session() as sess:
            _server = sess.query(MCPServer).filter(MCPServer.id == req.server_id).first()
            if not _server:
                raise ServiceError("MCP Server not found")
            if _server.server_online != req.server_online:
                if req.server_online == 1:
                    try:
                        await mcp_server.domain_server.multi_manager.add_mcp_session(_server.server_name,
                                                                                     _server.server_url)
                    except Exception as e:
                        logger.error(f"Failed to add MCP session: {e}")
                        _server.server_online = 0
                        _server.server_status = "running failed"
                        sess.commit()
                        raise ServiceError("Failed to add MCP session")
                    _server.server_status = "running success"
                else:
                    await MCPService.mcp_server_offline(_server)
                    _server.server_status = "offline"
                _server.server_online = req.server_online
                sess.commit()

    @staticmethod
    def update_or_create_mcp_server_tool(req: UpdateMCPServerToolReq):
        with orm_session() as sess:
            tool = sess.query(MCPServerTool).where(
                and_(MCPServerTool.server_id == req.server_id, MCPServerTool.tool_name == req.tool_name)
            ).first()
            if tool:
                tool.tool_domains = req.tool_domains or tool.tool_domains
                tool.need_require_auth = 1 if req.need_require_auth > 0 else 0
            else:
                tool = MCPServerTool(
                    server_id=req.server_id,
                    tool_name=req.tool_name,
                    tool_domains=req.tool_domains,
                    need_require_auth=req.need_require_auth
                )
            sess.add(tool)
            sess.commit()
            return tool.to_dict()

    @staticmethod
    async def create_mcp_server(req: CreateMCPServerReq):
        with orm_session() as sess:
            existing_server_by_url = sess.query(MCPServer).filter(MCPServer.server_url == req.server_url).first()
            if existing_server_by_url:
                logger.warning(f"server_url {req.server_url} already exists")
                raise ServiceError(f"server_url {req.server_url} already exists")

        # 检查数据库中是否已存在相同的 server_name
        with orm_session() as sess:
            existing_server_by_name = sess.query(MCPServer).filter(MCPServer.server_name == req.server_name).first()
            if existing_server_by_name:
                logger.warning(f"server_name {req.server_name} already exists")
                raise ServiceError(f"server_name {req.server_name} already exists")
        try:
            logger.info(f"Adding new session: {req.server_name} with URL: {req.server_url}")
            await mcp_server.domain_server.multi_manager.add_mcp_session(req.server_name, req.server_url)
            logger.info(f"Successfully initialized session: {req.server_name}")
            with orm_session() as sess:
                new_server = MCPServer(
                    server_name=req.server_name,
                    server_url=req.server_url,
                    public_status=req.public_status,
                    emails=req.emails,
                    created_by=req.created_by,
                    updated_by=req.created_by,
                )
                sess.add(new_server)
                sess.commit()
                return new_server.to_dict()
        except Exception as e:
            logger.error(f"Failed to initialize session {req.server_name}: {e.args}")
            if req.server_url in mcp_server.domain_server.multi_manager.sessions:
                del mcp_server.domain_server.multi_manager.sessions[req.server_url]
            raise ServiceError(f"server connect error：{e.args}")

    @staticmethod
    async def delete_mcp_server(server_id: int):
        with orm_session() as sess:
            _server = sess.query(MCPServer).filter(MCPServer.id == server_id).first()
            if not _server:
                logger.warning(f"MCP Server with ID {server_id} does not exist")
                raise ServiceError(f"MCP Server with ID {server_id} does not exist")
            sess.query(MCPServerTool).filter(MCPServerTool.server_id == server_id).delete()
            sess.delete(_server)
            sess.commit()
            await MCPService.mcp_server_offline(_server)

    @staticmethod
    async def mcp_server_offline(_server: MCPServer):
        if _server.server_url in mcp_server.domain_server.multi_manager.sessions:
            del mcp_server.domain_server.multi_manager.sessions[_server.server_url]

    @staticmethod
    def list_mcp_tool_domain_list() -> List[Dict[str, Any]]:
        with orm_session() as sess:
            domains = sess.query(MCPServerDomain).all()
            domain_list = []
            for domain in domains:
                domain_list.append({
                    "domain": domain.domain_id,
                    "domain_name": domain.domain_name,
                })
            return domain_list

    @staticmethod
    def list_mcp_domain_list() -> List[Dict[str, Any]]:
        with orm_session() as sess:
            domains = sess.query(MCPServerDomain).all()
            for domain in domains:
                domain.domain_name = domain.domain_name
            return [domain.to_dict() for domain in domains]

    # 新增工具领域
    @staticmethod
    def create_mcp_tool_domain(req: CreateMCPServerToolDomainReq):
        with orm_session() as sess:
            existing_domain = sess.query(MCPServerDomain).filter(MCPServerDomain.domain_id == req.domain_id).first()
            if existing_domain:
                logger.warning(f"domain_id {req.domain_id} already exists")
                raise ServiceError(f"domain_id {req.domain_id} already exists")
            new_domain = MCPServerDomain(
                domain_id=req.domain_id,
                domain_name=req.domain_name,
                description=req.description,
                created_by=req.created_by,
                updated_by=req.created_by,
            )
            sess.add(new_domain)
            sess.commit()
            return new_domain.to_dict()

    @staticmethod
    def update_mcp_tool_domain(req: UpdateMCPServerToolDomainReq):
        with orm_session() as sess:
            domain = sess.query(MCPServerDomain).filter(MCPServerDomain.domain_id == req.domain_id).first()
            if not domain:
                logger.warning(f"domain_id {req.domain_id} does not exist")
                raise ServiceError(f"domain_id {req.domain_id} does not exist")
            domain.domain_name = req.domain_name
            domain.description = req.description
            domain.updated_by = req.updated_by
            sess.commit()
            return domain.to_dict()

    @staticmethod
    def delete_mcp_tool_domain(domain_id: str):
        with orm_session() as sess:
            domain = sess.query(MCPServerDomain).filter(MCPServerDomain.domain_id == domain_id).first()
            if not domain:
                logger.warning(f"领域ID {domain_id} 不存在")
                raise ServiceError(f"领域ID {domain_id} 不存在")
            sess.delete(domain)
            sess.commit()

    async def create_mcp_domain_description(self, domain_id: str):
        with orm_session() as sess:
            domain = sess.query(MCPServerDomain).filter(MCPServerDomain.domain_id == domain_id).first()
            if not domain:
                logger.warning(f"领域ID {domain_id} 不存在")
                raise ServiceError(f"领域ID {domain_id} 不存在")

            tools = await self.list_mcp_server_tool(server_id=None, domain=domain.domain_id, tool_name=None)

            tools_desc = []
            for tool in tools:
                tools_desc.append(
                    {
                        "工具名称:": tool.tool_name,
                        "工具描述:": tool.description,
                    }
                )
            system_prompt = '''
                              # 角色
                                你是一个领域工具集的功能描述优化专家。

                              # 领域工具集名称
                                {domain_name}
                                
                              # 领域现有描述
                                {domain_description}

                              # 领域工具集工具列表
                                {tools}

                              # 优化要求  
                               1.请描述给定领域工具集的功能，确保描述精准且简要，概括性得进行描述，而不要具体到工具的细节。
                               2.不要捏造工具所具备的功能，工具的功能要以工具描述为准。不要夸大工具作用。
                               2.请不要包含对这些功能的总结性描述。
                               
                              # 请你遵守优化要求，输出优化后结果 
                            '''
            prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", system_prompt),
                ]
            )

            chain = prompt | get_llm()

            output = chain.invoke(
                {
                    "domain_name": f"{domain.domain_id}({domain.domain_name})领域工具集",
                    "domain_description": domain.description,
                    "tools": tools_desc
                }
            )
            return output.content

    async def create_mcp_tool_description(self, server_id: int, tool_name: str):
        with orm_session() as sess:
            server = sess.query(MCPServer).filter(MCPServer.id == server_id).first()
            if not server:
                logger.warning(f"服务ID {server_id} 不存在")
                raise ServiceError(f"服务ID {server_id} 不存在")
            tool = await self.get_mcp_server_tool(server_id=server_id, tool_name=tool_name)
            if not tool:
                logger.warning(f"未找到工具 {tool_name} 在服务器 {server_id}")
                raise ServiceError(f"未找到工具 {tool_name} 在服务器 {server_id}")
            system_prompt = '''
                              # 角色
                                你是一个工具描述优化专家，请你按照格式和优化要求，对当前工具描述进行优化。

                              # 工具名称
                                {tool_name}

                              # 当前工具描述
                                {current_description}
                                
                              # 工具描述模版
                                  - 功能说明：
                                    工具用于发起对堡垒机系统中指定主机账号的访问权限申请流程,该工具专注于权限申请操作，不具备查询主机信息、远程登录或执行命令等其他功能。
                                  - 输入参数：
                                    hosts：主机名称或IP地址（必填），支持多个主机，使用英文逗号分隔。
                                    accounts：主机账号列表(必填)，支持多个账号，使用英文逗号分隔。
                                  - 注意事项：
                                    1.禁止申请root账号。
                                    2.当用户未提供账号时，必须说明：可申请账号：__su账号、godman账号、log_view账号、同名账号(用户邮箱前缀)。
                                  - 核心特性：
                                    支持批量主机与账号权限申请
                                  - 典型场景：
                                    用户需要访问一个主机或一组主机以进行运维或开发操作，通过该工具申请对应主机账号的访问权限。

                              # 优化要求  
                               1.请对提供的工具描述进行润色优化，确保语义清晰、表达专业。
                               2.不要捏造工具所具备的功能，工具的功能要以当前描述为准。不要夸大工具作用。
                               3.严格按照工具描述模版，进行优化，必须包含模版中功能说明、输入参数、注意事项、核心特性、典型场景
                               3.严格按照工具模版输出仅包含优化后的描述，无需额外解释。
                            '''
            prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", system_prompt),
                ]
            )

            chain = prompt | get_llm()

            output = chain.invoke(
                {
                    "tool_name": tool.tool_name,
                    "current_description": tool.description,
                }
            )
            print(output.content)
            return output.content


mcp_Service = MCPService()
