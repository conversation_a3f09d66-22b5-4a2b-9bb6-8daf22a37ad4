from src.core.schema import *
from src.application.mcp import MCPApplication
from src.infra.web.http import http_server

__all__ = ["router"]

router = http_server.new_router()


@router.get("/mcp/server/list")  # 查询服务列表s
async def list_mcp_server():
    return await MCPApplication.list_mcp_server()


@router.get("/mcp/server/tool/list")  # 查询服务工具列表
async def list_mcp_server_tool(server_id: int = None, domain: str = None, tool_name: str = None):
    return await MCPApplication.list_mcp_server_tool(server_id, domain, tool_name)


@router.post("/mcp/server/create")
async def create_mcp_server(req: CreateMCPServerReq):
    return await MCPApplication.create_mcp_server(req)


@router.delete("/mcp/server/delete")
async def delete_mcp_server(server_id: int):
    return await MCPApplication.delete_mcp_server(server_id)


@router.put("/mcp/server/update")
def update_mcp_server(req: UpdateMCPServerReq):
    return MCPApplication.update_mcp_server(req)


@router.put("/mcp/server/online/status/update")
async def update_mcp_server_online_status(req: UpdateMCPServerOnlineStatusReq):
    await MCPApplication.update_mcp_server_online_status(req)


@router.put("/mcp/server/tool/update")
def update_mcp_server_tool(req: UpdateMCPServerToolReq):
    return MCPApplication.update_mcp_server_tool(req)


@router.get("/mcp/tool/domain/list")
def list_mcp_tool_domain_list():
    return MCPApplication.list_mcp_tool_domain_list()


@router.get("/mcp/domain/list")
def list_mcp_domain_list():
    return MCPApplication.list_mcp_domain_list()


@router.post("/mcp/domain/create")
def create_mcp_domain(req: CreateMCPServerToolDomainReq):
    return MCPApplication.create_mcp_domain(req)


@router.put("/mcp/domain/update")
def update_mcp_domain(req: UpdateMCPServerToolDomainReq):
    return MCPApplication.update_mcp_domain(req)


@router.delete("/mcp/domain/delete")
def delete_mcp_domain(domain_id: str):
    return MCPApplication.delete_mcp_domain(domain_id)


@router.post("/mcp/domain/description/create")
async def create_mcp_domain_description(req: CreateMCPDomainDescriptionReq):
    return await MCPApplication.create_mcp_domain_description(req)


@router.post("/mcp/tool/description/create")
async def create_mcp_tool_description(req: CreateMCPToolDescriptionReq):
    return await MCPApplication.create_mcp_tool_description(req)
