import json
import time
from asyncio import Abstract<PERSON><PERSON><PERSON><PERSON>
from typing import Optional

import uvicorn
from fastapi import FastAPI, APIRouter, Request
from fastapi.exceptions import RequestValidationError
from starlette import status
from starlette.exceptions import HTTPException as StarletteHTTPException
from starlette.responses import <PERSON><PERSON><PERSON><PERSON>ponse, StreamingResponse
from src.infra.tools import exceptions
from src.infra.web import const
from src.infra.config.config import MCPConfig, new_app_config, MCPRunMode
from src.infra.web.do import HttpResponse
from src.infra.web.log import logger

__all__ = [
    "http_server",
]


class MCPWeb(FastAPI):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # infra
        self.config: MCPConfig = new_app_config()
        self.openapi_url = self.config.web_app.openapi_url
        self.docs_url = self.config.web_app.docs_url
        self.logger = logger
        # tools
        self.mode: Optional[MCPRunMode] = None
        self.init()

    @property
    def app_version(self) -> str:
        return self.config.web_app.version

    def _init_logger(self):
        self.logger.setLevel(self.config.web_app.log_level)

    def init(self):
        self._init_logger()
        self.mode = MCPRunMode(mode=self.config.web_app.mode)

    def new_router(self) -> APIRouter:
        return APIRouter()

    def register_router(self, **kwargs) -> "MCPWeb":
        self.include_router(**kwargs)
        return self

    def launch(self, event_loop: AbstractEventLoop = None):
        if event_loop is not None:
            config = uvicorn.Config(http_server,
                                    host=self.config.web_app.host,
                                    port=self.config.web_app.port,
                                    log_config=const.LOGGING_CONFIG,
                                    log_level=str.lower(self.config.web_app.log_level),
                                    loop=event_loop)
            server = uvicorn.Server(config)
            event_loop.run_until_complete(server.serve())
        else:

            uvicorn.run(self,
                        host=self.config.web_app.host,
                        port=self.config.web_app.port,
                        log_config=const.LOGGING_CONFIG,
                        log_level=str.lower(self.config.web_app.log_level),
                        )


http_server = MCPWeb(timeout_client=600)


def _ignore_wrap_response(request: Request, response: StreamingResponse) -> bool:
    if response.headers["Content-Type"] != "application/json":
        return True

    if request.url.path == http_server.config.web_app.docs_url:
        return True

    if request.url.path == http_server.config.web_app.openapi_url:
        return True

    return False


@http_server.middleware("stander_response")
async def wrap_response(request: Request, call_next):
    start_time = time.time()
    response: StreamingResponse = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)

    if _ignore_wrap_response(request, response):
        return response

    body_container = []

    async for chunk in response.body_iterator:
        body_container.append(str(chunk, encoding="utf-8"))

    resp_data = json.loads(body_container[0])
    data = HttpResponse()
    if response.status_code >= 400:
        data.code = const.CODE_ERROR
        data.message = resp_data
    else:
        data.code = const.CODE_SUCCEED
        data.data = resp_data

    data_str = data.json().encode("utf-8")
    body_container = [data_str]
    # 设置头部长度
    response.headers["Content-Length"] = str(len(data_str))

    async def new_body_iterator():
        yield body_container[0]

    response.body_iterator = new_body_iterator()
    return response


@http_server.exception_handler(Exception)
async def common_exception_handler(request, exc: Exception):
    resp = HttpResponse()
    resp.code = const.CODE_ERROR
    resp.message = "Unknown error." if str(exc) == "" else str(exc)
    return JSONResponse(content=resp.dict(), status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@http_server.exception_handler(StarletteHTTPException)
async def http_exception_handler(request, exc: StarletteHTTPException):
    resp = HttpResponse()
    resp.code = const.CODE_ERROR
    resp.message = "Unknown error." if str(exc) == "" else str(exc)
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR if exc.status_code == 0 else exc.status_code
    return JSONResponse(content=resp.dict(), status_code=status_code)


@http_server.exception_handler(RequestValidationError)
async def validation_exception_handler(request, err: RequestValidationError):
    resp = HttpResponse()
    resp.code = const.CODE_ERROR
    resp.message = "Unprocessable entity."
    resp.data = err.errors()
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    return JSONResponse(content=resp.dict(), status_code=status_code)


@http_server.exception_handler(exceptions.NotFoundError)
async def validation_exception_handler(request, err: exceptions.NotFoundError):
    http_server.logger.exception(err)
    status_code = status.HTTP_404_NOT_FOUND
    return JSONResponse(content=err.errors(), status_code=status_code)


@http_server.exception_handler(exceptions.NotFoundError)
async def validation_exception_handler(request, err: exceptions.NotFoundError):
    http_server.logger.exception(err)
    status_code = status.HTTP_404_NOT_FOUND
    return JSONResponse(content=err.errors(), status_code=status_code)


@http_server.exception_handler(exceptions.ServiceError)
async def validation_exception_handler(request, err: exceptions.ServiceError):
    http_server.logger.exception(err)
    status_code = status.HTTP_400_BAD_REQUEST
    return JSONResponse(content=err.errors(), status_code=status_code)
