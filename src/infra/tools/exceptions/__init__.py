from typing import Any

__all__ = [
    "NotFoundError",
    "raise_not_found_error",
    "ServiceError"
]


class NotFoundError(Exception):
    """
    NotFoundError
    """

    def __init__(self, message: str):
        self.message = message + " not found."
        super().__init__(self.message)

    def errors(self) -> str:
        return self.message


def raise_not_found_error(object: Any, error: str = "xxx"):
    if object is None:
        raise NotFoundError(error)


class ServiceError(Exception):
    """
    NotFoundError
    """

    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)

    def errors(self) -> str:
        return self.message
