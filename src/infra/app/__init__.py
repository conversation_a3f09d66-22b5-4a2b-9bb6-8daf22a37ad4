import asyncio
from asyncio import Abstract<PERSON><PERSON><PERSON><PERSON>
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, Future
from typing import Optional, List

from psycopg_pool import AsyncConnectionPool

from src.infra import clients
from src.infra.clients.orm import init_db_engine, init_orm_session
from src.infra.config import MCPConfig
from src.infra.web.http import http_server
from src.infra.web.log import logger


class App:

    def __init__(self, config: MCPConfig):
        self.config = config
        self.thread_pool = ThreadPoolExecutor(max_workers=self.config.web_app.thread_pool_size)

        self._postgres_obj = clients.PostgresqlCli(config=self.config)
        self.postgres_cli = self._postgres_obj.init_sync_pg()
        self.apostgres_cli: Optional[AsyncConnectionPool] = None

        # self._milvus_obj = clients.MilvusCli(config=self.config)
        # self.milvus_cli = self._milvus_obj.init_sync_milvus()
        # self.amilvus_cli: Optional[AsyncMilvusClient] = None

        self.logger = logger
        self.http_server = http_server

        self._on_starts = []
        self._on_start_futures: List[Future] = []
        self._on_stops = []

    async def async_init(self) -> None:
        self.apostgres_cli = await self._postgres_obj.init_async_pg()
        self._init_async_orm()

        # self.amilvus_cli = await self._milvus_obj.init_async_milvus()

    def _init_async_orm(self):
        db_engine = init_db_engine(self.config)
        init_orm_session(db_engine)

    def add_on_start(self, func: callable) -> None:
        self._on_starts.append(func)

    def add_on_stop(self, func: callable) -> None:
        self._on_stops.append(func)

    def _on_start_with_thread(self) -> None:
        if self.thread_pool is None:
            raise ValueError("thread pool is not initialized")

        def function_wrapper(func):
            try:
                if asyncio.iscoroutinefunction(func):
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(func())  # 运行异步函数
                    loop.close()
                else:
                    func()
            except Exception as e:
                logger.exception(f"Error in thread pool: {e}")

        for func in self._on_starts:
            future = self.thread_pool.submit(function_wrapper, func)
            self._on_start_futures.append(future)

    def _on_stop_with_thread(self) -> None:
        if self.thread_pool is None:
            raise ValueError("thread pool is not initialized")

        def function_wrapper(func):
            try:
                if asyncio.iscoroutinefunction(func):  # 判断 func 是否是异步函数
                    loop = asyncio.new_event_loop()  # 创建一个新的事件循环
                    asyncio.set_event_loop(loop)  # 设置这个新循环为当前线程的事件循环
                    loop.run_until_complete(func())  # 在新的事件循环中运行异步任务
                    loop.close()  # 关闭事件循环
                else:
                    func()
            except Exception as e:
                logger.exception(f"Error in thread pool: {e}")

        _futures: List[Future] = []
        for func in self._on_stops:
            _futures.append(self.thread_pool.submit(function_wrapper, func))

    def submit_concurrent_task(self, func: callable, *args, **kwargs) -> Future:
        def wrap_func(func, *args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.exception(f"concurrent task error: {e}")

        return self.thread_pool.submit(wrap_func, func, *args, **kwargs)

    def launch(self, loop: AbstractEventLoop = None):

        self.http_server.add_event_handler("startup", self._on_start_with_thread)
        self.http_server.add_event_handler("shutdown", self._on_stop_with_thread)

        self.http_server.launch(loop)


app = App(config=http_server.config)
