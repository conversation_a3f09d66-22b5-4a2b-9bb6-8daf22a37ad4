import json
from contextlib import contextmanager
from datetime import datetime
from typing import Dict, Any

from sqlalchemy import Column, Integer, DateTime, Date, create_engine, Engine, text
from sqlalchemy.orm import DeclarativeBase, sessionmaker

from src.infra.config import MCPConfig
from src.infra.web.log import logger


def init_db_engine(config: MCPConfig) -> Engine:
    # Change the connection string (remove +asyncpg)
    link = f"postgresql://{config.postgres.user}:{config.postgres.password}@{config.postgres.host}:{config.postgres.port}/{config.postgres.database}"

    # Use create_engine instead of create_async_engine
    engine = create_engine(
        link,
        pool_size=config.postgres.min_size,
        max_overflow=config.postgres.max_size,
        echo=config.postgres.debug,  # 显示生成的 SQL (调试用)
    )

    try:
        # Test connection synchronously
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            conn.commit()
        logger.info("Database orm connection successful")
    except Exception as e:
        raise Exception(f"Database orm connection failed: {e}")

    return engine


class BaseModel(DeclarativeBase):
    __allow_unmapped__ = True

    def to_dict(self) -> Dict[str, Any]:
        res = {}
        for c in self.__table__.columns:
            res[c.name] = getattr(self, c.name)
        return res

    def to_json(self) -> str:
        res = {}
        for c in self.__table__.columns:
            value = getattr(self, c.name)
            if isinstance(value, datetime):
                value = value.timestamp() * 1000
            res[c.name] = value
        return json.dumps(res)


class IdModelMixin(BaseModel):
    __abstract__ = True
    id = Column(Integer, primary_key=True, autoincrement=True)


class DateTimeModelMixin(BaseModel):
    __abstract__ = True
    created_at = Column(DateTime, default=datetime.now, nullable=False, comment='创建日期')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=False, comment='更新日期')


class DateModelMixin(BaseModel):
    __abstract__ = True
    created_at = Column(Date, default=datetime.now, nullable=False, comment='创建日期')
    updated_at = Column(Date, default=datetime.now, onupdate=datetime.now, nullable=False, comment='更新日期')


def init_orm_session(engine: Engine):
    global SessionLocal
    # Use regular sessionmaker instead of async_sessionmaker
    SessionLocal = sessionmaker(
        bind=engine,
        expire_on_commit=False
    )


# Use regular contextmanager instead of asynccontextmanager
@contextmanager
def orm_session():
    # Create a new session
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    finally:
        session.close()
