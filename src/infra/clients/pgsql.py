from typing import Optional

import psycopg
from psycopg_pool import ConnectionPool, AsyncConnectionPool
from pydantic import Field, BaseModel

from src.infra.config import MCPConfig, get_or_creat_settings_ins

__all__ = [
    "PostgresqlCli",
]

from src.infra.web.log import logger


class PostgresqlCli(BaseModel):
    config: MCPConfig = Field(default_factory=get_or_creat_settings_ins)

    def init_sync_pg(self) -> Optional[ConnectionPool]:
        """初始化同步连接池"""
        if not self.config.postgres.host:
            logger.warning("Postgres config is not set.")
            return None

        try:
            pool = ConnectionPool(
                conninfo=(
                    f"postgresql://{self.config.postgres.user}:{self.config.postgres.password}"
                    f"@{self.config.postgres.host}:{self.config.postgres.port}"
                    f"/{self.config.postgres.database}"
                ),
                min_size=self.config.postgres.min_size,
                max_size=self.config.postgres.max_size,
            )
            pool.wait()  # 等待连接池初始化
            # 验证连接有效性
            with pool.connection() as conn:

                with conn.cursor() as cur:
                    cur.execute("SELECT 1")
                    logger.debug("Sync Postgres connection test succeeded")

            logger.info("Sync Postgres connection pool initialized (psycopg3)")
            return pool
        except psycopg.Error as e:
            logger.error(f"Sync Postgres connection failed: {str(e)}")
            return None

    async def init_async_pg(self) -> Optional[AsyncConnectionPool]:
        """初始化异步连接池"""
        if not self.config.postgres.host:
            logger.warning("Postgres config is not set.")
            return None

        try:
            pool = AsyncConnectionPool(
                conninfo=(
                    f"postgresql://{self.config.postgres.user}:{self.config.postgres.password}"
                    f"@{self.config.postgres.host}:{self.config.postgres.port}"
                    f"/{self.config.postgres.database}"
                ),
                min_size=self.config.postgres.min_size,
                max_size=self.config.postgres.max_size,
                open=False,
                kwargs={
                    "autocommit": False,
                },

            )
            await pool.open()

            logger.info("Async Postgres connection pool initialized (psycopg3)")
            return pool
        except psycopg.Error as e:
            logger.error(f"Async Postgres connection failed: {str(e)}")
            return None
