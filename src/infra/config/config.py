import os

import yaml
from pydantic import BaseModel, Field

from src.infra.web import const
from src.infra.web.const import DEV_MODE, PROD_MODE, TEST_MODE, APP_CONFIG_FILE


class WebApp(BaseModel):
    version: str = Field(default="v0.0.0")
    host: str = Field(default="0.0.0.0")
    port: int = Field(default=8000)
    log_level: str = Field(default="info", examples=["INFO", "DEBUG", "ERROR"])
    openapi_url: str = Field(default="/openapi.json")
    thread_pool_size: int = Field(default=50, description="线程池大小")
    process_pool_size: int = Field(default=5, description="进程池大小")
    docs_url: str = Field(default="/docs")
    mode: str = Field(default=DEV_MODE, examples=[DEV_MODE, PROD_MODE, TEST_MODE])
    max_workers: int = Field(default=10)


class MCPApp(BaseModel):
    host: str = Field(default="0.0.0.0")
    port: int = Field(default=8001)


class PostgresSettings(BaseModel):
    user: str = Field(..., description="数据库用户名")
    password: str = Field(..., description="数据库密码")
    host: str = Field(..., description="数据库主机")
    port: int = Field(5432, description="数据库端口")
    database: str = Field(..., description="数据库名")
    min_size: int = Field(1, description="连接池最小连接数")
    max_size: int = Field(10, description="连接池最大连接数")
    conn_timeout: int = Field(10, description="连接超时时间（秒）")
    pool_recycle: int = Field(3600, description="连接池回收时间（秒）")
    debug: bool = Field(False, description="是否开启调试模式")


class HostApi(BaseModel):
    host: str = Field(..., description="host api host")


class LLMConfig(BaseModel):
    deployment_name: str = Field(..., description="部署名称")
    azure_endpoint: str = Field(..., description="azure endpoint")
    openai_api_version: str = Field(..., description="openai api version")
    openai_api_key: str = Field(..., description="openai api key")


class ErrorCode(BaseModel):
    wiki_took: str = Field(..., description="wiki took")
    wiki_sheet: str = Field(..., description="wiki sheet")


class Jarvis(BaseModel):
    app_id: str = Field(..., description="app id")
    app_secret: str = Field(..., description="app secret")


class MCPConfig(BaseModel):
    web_app: WebApp = Field(default=WebApp())
    postgres: PostgresSettings = Field(..., description="postgres settings")
    mcp_app: MCPApp = Field(default=MCPApp())
    host_api: HostApi = Field(..., description="host api")
    llm: LLMConfig = Field(..., description="llm config")
    error_code: ErrorCode = Field(..., description="error code")
    jarvis: Jarvis = Field(..., description="jarvis config")


def new_app_config(config_file: str = None) -> MCPConfig:
    if os.getenv(const.ENV_CONFIG) is not None:
        config_file = os.getenv(const.ENV_CONFIG)

    if config_file is None:
        config_file = APP_CONFIG_FILE

    print(f"load config: {config_file}")
    with open(config_file, "r", encoding="utf-8") as fd:
        result = yaml.load(fd.read(), Loader=yaml.FullLoader)
    return MCPConfig(**result)


# Mode
class MCPRunMode:
    def __init__(self, mode: str = DEV_MODE):
        self.mode = mode

    def is_dev(self) -> bool:
        return self.mode == DEV_MODE

    def is_prod(self) -> bool:
        return self.mode == PROD_MODE

    def is_test(self) -> bool:
        return self.mode == TEST_MODE

    def set_mode(self, mode: str) -> None:
        self.mode = mode

    def get_mode(self) -> str:
        return self.mode
