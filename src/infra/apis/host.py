from urllib.parse import urljoin

import requests

from src.infra.config import MCPConfig


class HostAPIWrapper:
    url: str
    token: str
    session: requests.Session

    def __init__(self, config: MCPConfig) -> None:
        self.url = config.host_api.host
        self.session = requests.session()

    def post(self, path: str, data=None, json=None, **kwargs) -> requests.Response:
        return self.session.post(
            urljoin(self.url, path), data=data, json=json, **kwargs
        )

    def get(self, path: str, **kwargs) -> requests.Response:
        return self.session.get(urljoin(self.url, path), **kwargs)
