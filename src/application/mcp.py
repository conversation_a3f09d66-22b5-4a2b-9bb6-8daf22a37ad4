from src.core.schema import *
from src.service.mcp import MCPService, mcp_Service


class MCPApplication:

    @staticmethod
    async def list_mcp_server():
        return await MCPService.list_mcp_server()

    @staticmethod
    async def list_mcp_server_tool(server_id: int, domain: str, tool_name: str):
        return await MCPService.list_mcp_server_tool(server_id, domain, tool_name)

    @staticmethod
    async def create_mcp_server(req: CreateMCPServerReq):
        return await MCPService.create_mcp_server(req)

    @staticmethod
    async def delete_mcp_server(server_id: int):
        return await MCPService.delete_mcp_server(server_id)

    @staticmethod
    def update_mcp_server(req: UpdateMCPServerReq):
        return MCPService.update_mcp_server(req)

    @staticmethod
    async def update_mcp_server_online_status(req: UpdateMCPServerOnlineStatusReq):
        await MCPService.update_mcp_server_online_status(req)

    @staticmethod
    def update_mcp_server_tool(req: UpdateMCPServerToolReq):
        return MCPService.update_or_create_mcp_server_tool(req)

    @staticmethod
    def list_mcp_tool_domain_list():
        return MCPService.list_mcp_tool_domain_list()

    @staticmethod
    def list_mcp_domain_list():
        return MCPService.list_mcp_domain_list()

    @staticmethod
    def create_mcp_domain(req: CreateMCPServerToolDomainReq):
        return MCPService.create_mcp_tool_domain(req)

    @staticmethod
    def update_mcp_domain(req: UpdateMCPServerToolDomainReq):
        return MCPService.update_mcp_tool_domain(req)

    @staticmethod
    def delete_mcp_domain(domain_id: str):
        return MCPService.delete_mcp_tool_domain(domain_id)

    @staticmethod
    async def create_mcp_domain_description(req: CreateMCPDomainDescriptionReq):
        return await mcp_Service.create_mcp_domain_description(domain_id=req.domain_id)

    @staticmethod
    async def create_mcp_tool_description(req: CreateMCPToolDescriptionReq):
        return await mcp_Service.create_mcp_tool_description(server_id=req.server_id, tool_name=req.tool_name)
