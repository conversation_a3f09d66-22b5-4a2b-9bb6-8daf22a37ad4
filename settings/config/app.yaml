web_app:
  version: v1.0.0
  host: 0.0.0.0
  port: 8000
  thread_pool_size: 20
  process_pool_size: 4
  # INFO, ERROR, DEBUG
  log_level: INFO
  openapi_url: /openapi.json
  docs_url: /docs
  # dev, test, pro
  mode: test
  # concurrence
  max_workers: 10

mcp_app:
  host: 0.0.0.0
  port: 8001

postgres:
  host: ***********
  port: 5432
  database: langgraph_checkpoint
  user: checkpoint_rw
  password: 5t8KV46htHrpGrQUASV8
  min_size: 1
  max_size: 10

host_api:
  host: "http://yw-jump-open-api.ttyuyin.com:1081"

llm:
  deployment_name: gpt-4o
  azure_endpoint: https://east-us-quwan-yw-infra-01.openai.azure.com
  openai_api_version: 2024-02-15-preview
  openai_api_key: ********************************

error_code:
  wiki_took: wikcnRAhgexWDgoIyQvOANCwPXb
  wiki_sheet: 0Eyfbj

jarvis:
  app_id: cli_a1d8686d873a500e
  app_secret: TEQqjQjYaRqGK4IFc2ADhfYNVhxs58gY