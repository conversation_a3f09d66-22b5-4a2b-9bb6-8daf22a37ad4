from mcp.server import FastMCP

from src.core.mcp.tools.errorcode import register_error_code_tools
from src.core.mcp.tools.permissions.host import register_host_tools
from src.core.mcp.tools.permissions.kubernetes import register_kubernetes_tools
from src.infra.config.config import new_app_config

if __name__ == "__main__":
    try:
        config = new_app_config()
        local_mcp = FastMCP("local")
        register_error_code_tools(local_mcp, config)
        register_host_tools(local_mcp, config)
        register_kubernetes_tools(local_mcp, config)
        local_mcp.settings.port = 8002
        local_mcp.run(transport="sse")
    except KeyboardInterrupt:
        print("\nReceived shutdown signal")
