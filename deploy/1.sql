CREATE TABLE mcp_server
(
    id            SERIAL PRIMARY KEY,
    server_name   VARCHAR(255) NOT NULL UNIQUE,
    server_url    VARCHAR(500) NOT NULL UNIQUE,
    server_online INTEGER      NOT NULL DEFAULT 1,
    server_status VARCHAR(255) NOT NULL DEFAULT 'running success',
    created_at    TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE mcp_server_tool
(
    id                SERIAL PRIMARY KEY,
    tool_name         VARCHAR(255) NOT NULL,
    tool_domains      TEXT[] NULL,
    need_require_auth INTEGER      NOT NULL DEFAULT 0,
    server_id         INTEGER      NOT NULL,
    created_at        TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at        TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_mcp_server_tool_server_id FOREIGN KEY (server_id) REFERENCES mcp_server (id)
);

-- Add an index for better performance
CREATE INDEX idx_mcp_server_tool_server_id ON mcp_server_tool (server_id);

ALTER TABLE mcp_server
    ADD COLUMN emails TEXT[] NULL;

ALTER TABLE mcp_server
    ADD COLUMN public_status INTEGER NOT NULL DEFAULT 1;

ALTER TABLE mcp_server
    ADD COLUMN tool_count INTEGER NOT NULL DEFAULT 0;

ALTER TABLE mcp_server
    ADD COLUMN created_by VARCHAR(255) NULL default NULL;

ALTER TABLE mcp_server
    ADD COLUMN updated_by VARCHAR(255) NULL default NULL;


ALTER TABLE mcp_server_tool
    ADD COLUMN description TEXT NULL;

CREATE TABLE mcp_server_domain
(
    id          SERIAL PRIMARY KEY,
    domain_id   VARCHAR(255) NOT NULL,
    domain_name VARCHAR(255) NOT NULL,
    description TEXT         NOT NULL,
    created_at  TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by  VARCHAR(255) NULL default NULL,
    updated_by  VARCHAR(255) NULL default NULL
);